import React, {useState} from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  FlatList,
} from 'react-native';
import {Card, Chip} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {colors, spacing, borderRadius, shadows} from '../theme/theme';

interface Surah {
  id: number;
  name: string;
  arabicName: string;
  verses: number;
  type: 'مكية' | 'مدنية';
  order: number;
}

const QuranScreen = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedSurah, setSelectedSurah] = useState<Surah | null>(null);

  const surahs: Surah[] = [
    {id: 1, name: 'الفاتحة', arabicName: 'الْفَاتِحَة', verses: 7, type: 'مكية', order: 1},
    {id: 2, name: 'البقرة', arabicName: 'الْبَقَرَة', verses: 286, type: 'مدنية', order: 2},
    {id: 3, name: 'آل عمران', arabicName: 'آل عِمْرَان', verses: 200, type: 'مدنية', order: 3},
    {id: 4, name: 'النساء', arabicName: 'النِّسَاء', verses: 176, type: 'مدنية', order: 4},
    {id: 5, name: 'المائدة', arabicName: 'الْمَائِدَة', verses: 120, type: 'مدنية', order: 5},
    {id: 6, name: 'الأنعام', arabicName: 'الْأَنْعَام', verses: 165, type: 'مكية', order: 6},
    {id: 7, name: 'الأعراف', arabicName: 'الْأَعْرَاف', verses: 206, type: 'مكية', order: 7},
    {id: 8, name: 'الأنفال', arabicName: 'الْأَنْفَال', verses: 75, type: 'مدنية', order: 8},
  ];

  const filteredSurahs = surahs.filter(surah =>
    surah.name.includes(searchQuery) || surah.arabicName.includes(searchQuery)
  );

  const renderSurahCard = ({item}: {item: Surah}) => (
    <TouchableOpacity
      style={styles.surahCard}
      onPress={() => setSelectedSurah(item)}>
      <Card style={styles.card}>
        <View style={styles.cardContent}>
          <View style={styles.surahNumber}>
            <Text style={styles.numberText}>{item.order}</Text>
          </View>
          <View style={styles.surahInfo}>
            <Text style={styles.surahName}>{item.name}</Text>
            <Text style={styles.surahArabic}>{item.arabicName}</Text>
            <Text style={styles.versesCount}>{item.verses} آية</Text>
          </View>
          <View style={styles.surahMeta}>
            <Chip
              style={[
                styles.typeChip,
                {backgroundColor: item.type === 'مكية' ? colors.green[100] : colors.primary + '20'}
              ]}
              textStyle={[
                styles.typeText,
                {color: item.type === 'مكية' ? colors.green[800] : colors.primary}
              ]}>
              {item.type}
            </Chip>
            <View style={styles.actions}>
              <TouchableOpacity style={styles.actionButton}>
                <Icon name="play-circle-outline" size={24} color={colors.primary} />
              </TouchableOpacity>
              <TouchableOpacity style={styles.actionButton}>
                <Icon name="bookmark-border" size={24} color={colors.gray[600]} />
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Card>
    </TouchableOpacity>
  );

  const renderSurahContent = () => {
    if (!selectedSurah) return null;

    const ayahs = selectedSurah.id === 1 ? [
      'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ ﴿١﴾',
      'الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ ﴿٢﴾',
      'الرَّحْمَٰنِ الرَّحِيمِ ﴿٣﴾',
      'مَالِكِ يَوْمِ الدِّينِ ﴿٤﴾',
      'إِيَّاكَ نَعْبُدُ وَإِيَّاكَ نَسْتَعِينُ ﴿٥﴾',
      'اهْدِنَا الصِّرَاطَ الْمُسْتَقِيمَ ﴿٦﴾',
      'صِرَاطَ الَّذِينَ أَنْعَمْتَ عَلَيْهِمْ غَيْرِ الْمَغْضُوبِ عَلَيْهِمْ وَلَا الضَّالِّينَ ﴿٧﴾'
    ] : ['نص السورة سيتم إضافته لاحقاً...'];

    return (
      <View style={styles.surahContent}>
        <View style={styles.surahHeader}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => setSelectedSurah(null)}>
            <Icon name="arrow-back" size={24} color="white" />
          </TouchableOpacity>
          <Text style={styles.surahTitle}>سورة {selectedSurah.name}</Text>
          <View style={styles.headerActions}>
            <TouchableOpacity style={styles.headerAction}>
              <Icon name="share" size={20} color="white" />
            </TouchableOpacity>
            <TouchableOpacity style={styles.headerAction}>
              <Icon name="bookmark" size={20} color="white" />
            </TouchableOpacity>
          </View>
        </View>

        <ScrollView style={styles.ayahsContainer}>
          <View style={styles.bismillah}>
            <Text style={styles.bismillahText}>
              بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ
            </Text>
          </View>

          {ayahs.map((ayah, index) => (
            <TouchableOpacity key={index} style={styles.ayahContainer}>
              <Text style={styles.ayahText}>{ayah}</Text>
            </TouchableOpacity>
          ))}
        </ScrollView>

        <View style={styles.playerControls}>
          <View style={styles.playerInfo}>
            <Text style={styles.reciterName}>القارئ: عبد الباسط عبد الصمد</Text>
            <Text style={styles.playTime}>00:00 / 04:32</Text>
          </View>
          <View style={styles.progressBar}>
            <View style={[styles.progress, {width: '30%'}]} />
          </View>
          <View style={styles.controls}>
            <TouchableOpacity style={styles.controlButton}>
              <Icon name="skip-previous" size={28} color="white" />
            </TouchableOpacity>
            <TouchableOpacity style={styles.playButton}>
              <Icon name="play-arrow" size={36} color="white" />
            </TouchableOpacity>
            <TouchableOpacity style={styles.controlButton}>
              <Icon name="skip-next" size={28} color="white" />
            </TouchableOpacity>
          </View>
        </View>
      </View>
    );
  };

  if (selectedSurah) {
    return renderSurahContent();
  }

  return (
    <View style={styles.container}>
      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <View style={styles.searchBar}>
          <Icon name="search" size={20} color={colors.gray[500]} />
          <TextInput
            style={styles.searchInput}
            placeholder="ابحث عن سورة..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholderTextColor={colors.gray[500]}
          />
        </View>
      </View>

      {/* Surahs List */}
      <FlatList
        data={filteredSurahs}
        renderItem={renderSurahCard}
        keyExtractor={item => item.id.toString()}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.listContainer}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  searchContainer: {
    padding: spacing.lg,
    backgroundColor: colors.surface,
    ...shadows.sm,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.gray[100],
    borderRadius: borderRadius.lg,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
  },
  searchInput: {
    flex: 1,
    marginLeft: spacing.sm,
    fontSize: 16,
    color: colors.text,
  },
  listContainer: {
    padding: spacing.lg,
  },
  surahCard: {
    marginBottom: spacing.md,
  },
  card: {
    backgroundColor: colors.surface,
    borderLeftWidth: 4,
    borderLeftColor: colors.primary,
    ...shadows.sm,
  },
  cardContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.lg,
  },
  surahNumber: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  numberText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  surahInfo: {
    flex: 1,
  },
  surahName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: spacing.xs,
  },
  surahArabic: {
    fontSize: 16,
    color: colors.primary,
    marginBottom: spacing.xs,
  },
  versesCount: {
    fontSize: 14,
    color: colors.textSecondary,
  },
  surahMeta: {
    alignItems: 'flex-end',
  },
  typeChip: {
    marginBottom: spacing.sm,
  },
  typeText: {
    fontSize: 12,
    fontWeight: '500',
  },
  actions: {
    flexDirection: 'row',
  },
  actionButton: {
    marginLeft: spacing.sm,
  },
  // Surah Content Styles
  surahContent: {
    flex: 1,
    backgroundColor: colors.background,
  },
  surahHeader: {
    backgroundColor: colors.primary,
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.lg,
    paddingHorizontal: spacing.lg,
  },
  backButton: {
    marginRight: spacing.md,
  },
  surahTitle: {
    flex: 1,
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white',
    textAlign: 'center',
  },
  headerActions: {
    flexDirection: 'row',
  },
  headerAction: {
    marginLeft: spacing.sm,
  },
  ayahsContainer: {
    flex: 1,
    padding: spacing.lg,
  },
  bismillah: {
    alignItems: 'center',
    marginBottom: spacing.xl,
    paddingVertical: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  bismillahText: {
    fontSize: 20,
    color: colors.primary,
    fontWeight: 'bold',
  },
  ayahContainer: {
    backgroundColor: colors.surface,
    padding: spacing.lg,
    marginBottom: spacing.md,
    borderRadius: borderRadius.md,
    ...shadows.sm,
  },
  ayahText: {
    fontSize: 18,
    lineHeight: 32,
    color: colors.text,
    textAlign: 'justify',
  },
  playerControls: {
    backgroundColor: colors.primary,
    padding: spacing.lg,
  },
  playerInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: spacing.sm,
  },
  reciterName: {
    color: 'white',
    fontSize: 14,
  },
  playTime: {
    color: 'white',
    fontSize: 14,
  },
  progressBar: {
    height: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 2,
    marginBottom: spacing.lg,
  },
  progress: {
    height: '100%',
    backgroundColor: 'white',
    borderRadius: 2,
  },
  controls: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  controlButton: {
    marginHorizontal: spacing.lg,
  },
  playButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
});
