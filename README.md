# حصن المسلم - تطبيق أندرويد إسلامي شامل

## نظرة عامة

تطبيق "حصن المسلم" هو تطبيق أندرويد إسلامي شامل يحتوي على المصحف الشريف مع التفسير، وحقيبة المسلم التي تشمل الأدعية والأذكار والأحكام الفقهية والسيرة النبوية، بالإضافة إلى أوقات الصلاة.

## المحرر
**ياسر الجبوري** - العراق

## الميزات الرئيسية

### 📖 المصحف الشريف
- عرض جميع سور القرآن الكريم
- إمكانية البحث في السور
- مشغل صوتي للاستماع للتلاوة
- حفظ المواضع المفضلة
- واجهة سهلة الاستخدام

### 📚 التفسير
- تفسير ابن كثير
- تفسير الطبري
- تفسير السعدي
- تفسير القرطبي
- تبويبات منظمة للمفسرين المختلفين

### 🎒 حقيبة المسلم
- **الأدعية والأذكار**: مجموعة شاملة من الأدعية اليومية
- **الأحكام الفقهية**: أحكام الطهارة والصلاة والصيام والزكاة
- **السيرة النبوية**: أحداث مهمة من حياة النبي صلى الله عليه وسلم

### 🕐 أوقات الصلاة
- أوقات الصلاة للمدن العراقية
- تنبيهات الصلاة
- اتجاه القبلة
- التقويم الهجري
- جدول أسبوعي لأوقات الصلاة

## التقنيات المستخدمة

- **React Native**: إطار العمل الأساسي
- **TypeScript**: للكتابة الآمنة
- **React Navigation**: للتنقل بين الشاشات
- **React Native Paper**: مكونات واجهة المستخدم
- **React Native Vector Icons**: الأيقونات
- **React Native Geolocation**: تحديد الموقع

## متطلبات التشغيل

- Node.js (الإصدار 16 أو أحدث)
- React Native CLI
- Android Studio
- JDK 11 أو أحدث

## التثبيت والتشغيل

### 1. تثبيت المتطلبات
```bash
npm install -g react-native-cli
```

### 2. تثبيت التبعيات
```bash
npm install
```

### 3. تثبيت التبعيات للأندرويد
```bash
cd android
./gradlew clean
cd ..
```

### 4. ربط الأيقونات (للأندرويد)
```bash
npx react-native link react-native-vector-icons
```

### 5. تشغيل التطبيق
```bash
# تشغيل Metro Bundler
npx react-native start

# في terminal آخر، تشغيل التطبيق على الأندرويد
npx react-native run-android
```

## هيكل المشروع

```
hisnulmuslim/
├── src/
│   ├── screens/           # شاشات التطبيق
│   │   ├── HomeScreen.tsx
│   │   ├── QuranScreen.tsx
│   │   ├── TafsirScreen.tsx
│   │   ├── MuslimKitScreen.tsx
│   │   └── PrayerTimesScreen.tsx
│   └── theme/             # ثيم التطبيق
│       └── theme.ts
├── android/               # ملفات الأندرويد
├── App.tsx               # الملف الرئيسي
├── package.json          # تبعيات المشروع
└── README.md            # هذا الملف
```

## الشاشات

### الشاشة الرئيسية
- ترحيب بالمستخدم
- روابط سريعة للميزات الرئيسية
- معلومات عن التطبيق والمحرر

### شاشة المصحف
- قائمة بجميع السور
- إمكانية البحث
- عرض نص السورة
- مشغل صوتي

### شاشة التفسير
- تبويبات للمفسرين المختلفين
- عرض التفسير مع النص القرآني
- إمكانية المشاركة والحفظ

### شاشة حقيبة المسلم
- تبويبات للأدعية والأحكام والسيرة
- تصنيف منظم للمحتوى
- واجهة سهلة التصفح

### شاشة أوقات الصلاة
- أوقات الصلاة اليومية
- اختيار المدينة العراقية
- اتجاه القبلة
- جدول أسبوعي

## التخصيص

### تغيير الألوان
يمكن تعديل ألوان التطبيق من ملف `src/theme/theme.ts`

### إضافة مدن جديدة
يمكن إضافة مدن عراقية جديدة في `src/screens/PrayerTimesScreen.tsx`

### إضافة محتوى جديد
يمكن إضافة أدعية أو أحكام أو أحداث سيرة جديدة في `src/screens/MuslimKitScreen.tsx`

## المساهمة

نرحب بالمساهمات لتحسين التطبيق. يرجى:

1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. إضافة التحسينات
4. إرسال Pull Request

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام الشخصي والتعليمي.

## التواصل

للاستفسارات والاقتراحات، يمكن التواصل مع المحرر ياسر الجبوري.

---

**جعله الله في ميزان حسناتنا وحسناتكم**
