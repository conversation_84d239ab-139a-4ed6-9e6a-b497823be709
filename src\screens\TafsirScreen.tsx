import React, {useState} from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import {Card} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {colors, spacing, borderRadius, shadows} from '../theme/theme';

const {width} = Dimensions.get('window');

interface Mufassir {
  id: string;
  name: string;
  description: string;
}

interface TafsirContent {
  ayah: string;
  tafsir: string;
}

const TafsirScreen = () => {
  const [selectedMufassir, setSelectedMufassir] = useState('ibn-kathir');
  const [selectedSurah, setSelectedSurah] = useState('الفاتحة');

  const mufassirun: Mufassir[] = [
    {
      id: 'ibn-kathir',
      name: 'ابن كثير',
      description: 'تفسير القرآن العظيم',
    },
    {
      id: 'tabari',
      name: 'الطبري',
      description: 'جامع البيان في تأويل القرآن',
    },
    {
      id: 'saadi',
      name: 'السعدي',
      description: 'تيسير الكريم الرحمن',
    },
    {
      id: 'qurtubi',
      name: 'القرطبي',
      description: 'الجامع لأحكام القرآن',
    },
  ];

  const tafsirContent: Record<string, TafsirContent[]> = {
    'ibn-kathir': [
      {
        ayah: 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
        tafsir: 'قال ابن جرير: الله أعلم بمراده بذلك. وقال بعضهم: هو إخبار من الله تعالى بأنه افتتح خلقه ببسم الله الرحمن الرحيم، ويستحب للقارئ أن يفتتح التلاوة ببسم الله الرحمن الرحيم، اقتداء بالقرآن، وسنة عن النبي صلى الله عليه وسلم.',
      },
      {
        ayah: 'الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ',
        tafsir: 'الحمد لله: الثناء على الله بصفاته التي كلها صفات كمال، وبنعمه الظاهرة والباطنة، الدينية والدنيوية، وفي ضمنه أمر لعباده أن يحمدوه، فهو المستحق للحمد وحده، وهو سبحانه المنشئ للخلق، القائم بأمورهم، المربي لجميع خلقه بنعمه، ولأوليائه بالإيمان والعمل الصالح.',
      },
      {
        ayah: 'الرَّحْمَٰنِ الرَّحِيمِ',
        tafsir: 'الرحمن الرحيم: اسمان دالان على أنه تعالى ذو الرحمة الواسعة العظيمة التي وسعت كل شيء، وعمت كل حي، وكتبها للمتقين المتبعين لأنبيائه ورسله. فالرحمن دال على الصفة القائمة به سبحانه، والرحيم دال على تعلقها بالمرحوم.',
      },
    ],
    'tabari': [
      {
        ayah: 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
        tafsir: 'يقول تعالى ذكره: ابتدئوا بتسمية الله وذكره قبل كل شيء، والله هو الذي له الألوهية والعبودية من جميع الخلق، الرحمن بجميع خلقه، الرحيم بالمؤمنين خاصة.',
      },
      {
        ayah: 'الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ',
        tafsir: 'يقول تعالى: الحمد لله الذي له الحمد كله، والشكر جميعه، دون سائر ما يعبد من دونه، ودون كل ما برأ من خلقه، لانفراده بخلق جميعهم، وانفراده بالنعم عليهم، وإحسانه إليهم، وكفايته إياهم.',
      },
    ],
    'saadi': [
      {
        ayah: 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
        tafsir: 'أي: أبتدئ بكل اسم لله تعالى، لأن لفظ "اسم" مفرد مضاف، فيعم جميع الأسماء الحسنى. والله علم على الرب تبارك وتعالى، يدل على جميع الأسماء الحسنى والصفات العلى.',
      },
      {
        ayah: 'الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ',
        tafsir: 'الحمد هو الوصف بالجميل الاختياري على وجه التبجيل والتعظيم، وهو أعم من الشكر، فإن الشكر مقابل النعمة، والحمد أعم من ذلك. ولهذا كان أهل الجنة يختتمون دعاءهم بالحمد لله رب العالمين.',
      },
    ],
  };

  const renderMufassirTab = (mufassir: Mufassir) => (
    <TouchableOpacity
      key={mufassir.id}
      style={[
        styles.tab,
        selectedMufassir === mufassir.id && styles.activeTab,
      ]}
      onPress={() => setSelectedMufassir(mufassir.id)}>
      <Text
        style={[
          styles.tabText,
          selectedMufassir === mufassir.id && styles.activeTabText,
        ]}>
        {mufassir.name}
      </Text>
    </TouchableOpacity>
  );

  const renderTafsirContent = () => {
    const content = tafsirContent[selectedMufassir] || [];
    const selectedMufassirInfo = mufassirun.find(m => m.id === selectedMufassir);

    return (
      <View style={styles.contentContainer}>
        <Card style={styles.headerCard}>
          <View style={styles.headerContent}>
            <Text style={styles.tafsirTitle}>
              تفسير سورة {selectedSurah} - {selectedMufassirInfo?.name}
            </Text>
            <Text style={styles.tafsirDescription}>
              {selectedMufassirInfo?.description}
            </Text>
          </View>
        </Card>

        {content.map((item, index) => (
          <Card key={index} style={styles.ayahCard}>
            <View style={styles.ayahContent}>
              <View style={styles.ayahHeader}>
                <Text style={styles.ayahText}>{item.ayah}</Text>
                <View style={styles.ayahActions}>
                  <TouchableOpacity style={styles.actionButton}>
                    <Icon name="share" size={20} color={colors.primary} />
                  </TouchableOpacity>
                  <TouchableOpacity style={styles.actionButton}>
                    <Icon name="bookmark-border" size={20} color={colors.primary} />
                  </TouchableOpacity>
                </View>
              </View>
              <View style={styles.tafsirTextContainer}>
                <Text style={styles.tafsirText}>{item.tafsir}</Text>
              </View>
            </View>
          </Card>
        ))}

        <View style={styles.navigationButtons}>
          <TouchableOpacity style={styles.navButton}>
            <Icon name="arrow-back" size={20} color="white" />
            <Text style={styles.navButtonText}>السابق</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.navButton}>
            <Text style={styles.navButtonText}>التالي</Text>
            <Icon name="arrow-forward" size={20} color="white" />
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {/* Surah Selector */}
      <Card style={styles.surahSelector}>
        <TouchableOpacity style={styles.selectorContent}>
          <Text style={styles.selectorLabel}>السورة المختارة:</Text>
          <View style={styles.selectorValue}>
            <Text style={styles.selectedSurah}>{selectedSurah}</Text>
            <Icon name="keyboard-arrow-down" size={24} color={colors.primary} />
          </View>
        </TouchableOpacity>
      </Card>

      {/* Mufassirun Tabs */}
      <View style={styles.tabsContainer}>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.tabsContent}>
          {mufassirun.map(renderMufassirTab)}
        </ScrollView>
      </View>

      {/* Tafsir Content */}
      <ScrollView
        style={styles.scrollContainer}
        showsVerticalScrollIndicator={false}>
        {renderTafsirContent()}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  surahSelector: {
    margin: spacing.lg,
    backgroundColor: colors.surface,
    ...shadows.sm,
  },
  selectorContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: spacing.lg,
  },
  selectorLabel: {
    fontSize: 16,
    color: colors.textSecondary,
  },
  selectorValue: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  selectedSurah: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.primary,
    marginRight: spacing.sm,
  },
  tabsContainer: {
    backgroundColor: colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  tabsContent: {
    paddingHorizontal: spacing.lg,
  },
  tab: {
    paddingVertical: spacing.lg,
    paddingHorizontal: spacing.lg,
    marginRight: spacing.md,
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  activeTab: {
    borderBottomColor: colors.primary,
  },
  tabText: {
    fontSize: 16,
    color: colors.textSecondary,
    fontWeight: '500',
  },
  activeTabText: {
    color: colors.primary,
    fontWeight: 'bold',
  },
  scrollContainer: {
    flex: 1,
  },
  contentContainer: {
    padding: spacing.lg,
  },
  headerCard: {
    backgroundColor: colors.surface,
    marginBottom: spacing.lg,
    ...shadows.sm,
  },
  headerContent: {
    padding: spacing.lg,
    alignItems: 'center',
  },
  tafsirTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.primary,
    marginBottom: spacing.sm,
    textAlign: 'center',
  },
  tafsirDescription: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: 'center',
  },
  ayahCard: {
    backgroundColor: colors.surface,
    marginBottom: spacing.lg,
    ...shadows.sm,
  },
  ayahContent: {
    padding: spacing.lg,
  },
  ayahHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.lg,
    paddingBottom: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  ayahText: {
    flex: 1,
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.primary,
    lineHeight: 28,
  },
  ayahActions: {
    flexDirection: 'row',
    marginLeft: spacing.md,
  },
  actionButton: {
    marginLeft: spacing.sm,
    padding: spacing.xs,
  },
  tafsirTextContainer: {
    backgroundColor: colors.gray[50],
    padding: spacing.lg,
    borderRadius: borderRadius.md,
    borderRightWidth: 3,
    borderRightColor: colors.primary,
  },
  tafsirText: {
    fontSize: 16,
    lineHeight: 26,
    color: colors.text,
    textAlign: 'justify',
  },
  navigationButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: spacing.xl,
  },
  navButton: {
    backgroundColor: colors.primary,
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
    borderRadius: borderRadius.md,
    minWidth: 100,
    justifyContent: 'center',
  },
  navButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    marginHorizontal: spacing.xs,
  },
});
