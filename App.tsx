import React from 'react';
import {NavigationContainer} from '@react-navigation/native';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import {Provider as PaperProvider} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {I18nManager, StatusBar} from 'react-native';

// Import screens
import HomeScreen from './src/screens/HomeScreen';
import QuranScreen from './src/screens/QuranScreen';
import TafsirScreen from './src/screens/TafsirScreen';
import MuslimKitScreen from './src/screens/MuslimKitScreen';
import PrayerTimesScreen from './src/screens/PrayerTimesScreen';

// Import theme
import {theme} from './src/theme/theme';

// Enable RTL layout
I18nManager.allowRTL(true);
I18nManager.forceRTL(true);

const Tab = createBottomTabNavigator();

const App = () => {
  return (
    <PaperProvider theme={theme}>
      <StatusBar backgroundColor={theme.colors.primary} barStyle="light-content" />
      <NavigationContainer>
        <Tab.Navigator
          screenOptions={({route}) => ({
            tabBarIcon: ({focused, color, size}) => {
              let iconName: string;

              switch (route.name) {
                case 'Home':
                  iconName = 'home';
                  break;
                case 'Quran':
                  iconName = 'book';
                  break;
                case 'Tafsir':
                  iconName = 'library-books';
                  break;
                case 'MuslimKit':
                  iconName = 'favorite';
                  break;
                case 'PrayerTimes':
                  iconName = 'access-time';
                  break;
                default:
                  iconName = 'home';
              }

              return <Icon name={iconName} size={size} color={color} />;
            },
            tabBarActiveTintColor: theme.colors.primary,
            tabBarInactiveTintColor: 'gray',
            tabBarStyle: {
              backgroundColor: 'white',
              borderTopWidth: 1,
              borderTopColor: '#e0e0e0',
            },
            headerStyle: {
              backgroundColor: theme.colors.primary,
            },
            headerTintColor: 'white',
            headerTitleStyle: {
              fontWeight: 'bold',
              fontSize: 18,
            },
          })}>
          <Tab.Screen
            name="Home"
            component={HomeScreen}
            options={{
              title: 'الرئيسية',
              headerTitle: 'حصن المسلم',
            }}
          />
          <Tab.Screen
            name="Quran"
            component={QuranScreen}
            options={{
              title: 'المصحف',
              headerTitle: 'المصحف الشريف',
            }}
          />
          <Tab.Screen
            name="Tafsir"
            component={TafsirScreen}
            options={{
              title: 'التفسير',
              headerTitle: 'التفسير',
            }}
          />
          <Tab.Screen
            name="MuslimKit"
            component={MuslimKitScreen}
            options={{
              title: 'حقيبة المسلم',
              headerTitle: 'حقيبة المسلم',
            }}
          />
          <Tab.Screen
            name="PrayerTimes"
            component={PrayerTimesScreen}
            options={{
              title: 'أوقات الصلاة',
              headerTitle: 'أوقات الصلاة',
            }}
          />
        </Tab.Navigator>
      </NavigationContainer>
    </PaperProvider>
  );
};

export default App;
