import React, {useState} from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  FlatList,
} from 'react-native';
import {Card} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {colors, spacing, borderRadius, shadows} from '../theme/theme';

interface Category {
  id: string;
  title: string;
  icon: string;
  color: string;
  items: any[];
}

interface Dua {
  id: number;
  title: string;
  arabic: string;
  translation?: string;
  source: string;
}

interface FiqhRule {
  id: number;
  title: string;
  description: string;
  details: string;
}

interface SirahEvent {
  id: number;
  title: string;
  description: string;
  year?: string;
}

const MuslimKitScreen = () => {
  const [selectedCategory, setSelectedCategory] = useState<string>('duas');

  const duas: Dua[] = [
    {
      id: 1,
      title: 'دعاء الاستيقاظ من النوم',
      arabic: 'الحمد لله الذي أحيانا بعد ما أماتنا وإليه النشور',
      translation: 'الحمد لله الذي أعطانا الحياة بعد أن أماتنا (بالنوم) وإليه المصير',
      source: 'رواه البخاري ومسلم',
    },
    {
      id: 2,
      title: 'دعاء دخول الخلاء',
      arabic: 'اللهم إني أعوذ بك من الخبث والخبائث',
      translation: 'اللهم إني أستعيذ بك من الشياطين الذكور والإناث',
      source: 'رواه البخاري ومسلم',
    },
    {
      id: 3,
      title: 'دعاء الخروج من الخلاء',
      arabic: 'غفرانك',
      translation: 'أطلب مغفرتك يا الله',
      source: 'رواه أبو داود والترمذي',
    },
    {
      id: 4,
      title: 'دعاء الوضوء',
      arabic: 'اللهم اغفر لي ذنبي ووسع لي في داري وبارك لي في رزقي',
      source: 'رواه الطبراني',
    },
  ];

  const fiqhRules: FiqhRule[] = [
    {
      id: 1,
      title: 'أحكام الطهارة',
      description: 'شروط الوضوء وفروضه وسننه',
      details: 'الوضوء له شروط وفروض وسنن يجب على المسلم معرفتها...',
    },
    {
      id: 2,
      title: 'أحكام الصلاة',
      description: 'شروط الصلاة وأركانها وواجباتها',
      details: 'للصلاة شروط وأركان وواجبات لا تصح الصلاة إلا بها...',
    },
    {
      id: 3,
      title: 'أحكام الصيام',
      description: 'شروط الصوم ومبطلاته ومستحباته',
      details: 'الصيام له شروط ومبطلات ومستحبات يجب معرفتها...',
    },
    {
      id: 4,
      title: 'أحكام الزكاة',
      description: 'شروط وجوب الزكاة ومقاديرها',
      details: 'الزكاة ركن من أركان الإسلام لها شروط ومقادير محددة...',
    },
  ];

  const sirahEvents: SirahEvent[] = [
    {
      id: 1,
      title: 'مولد النبي صلى الله عليه وسلم',
      description: 'ولد النبي محمد صلى الله عليه وسلم في مكة المكرمة',
      year: '571 م',
    },
    {
      id: 2,
      title: 'بعثته صلى الله عليه وسلم',
      description: 'بدء الوحي في غار حراء والدعوة إلى الإسلام',
      year: '610 م',
    },
    {
      id: 3,
      title: 'الهجرة النبوية',
      description: 'هجرة النبي وأصحابه من مكة إلى المدينة المنورة',
      year: '622 م',
    },
    {
      id: 4,
      title: 'فتح مكة',
      description: 'فتح مكة المكرمة ودخول الناس في دين الله أفواجاً',
      year: '630 م',
    },
  ];

  const categories: Category[] = [
    {
      id: 'duas',
      title: 'الأدعية والأذكار',
      icon: 'favorite',
      color: colors.primary,
      items: duas,
    },
    {
      id: 'fiqh',
      title: 'الأحكام الفقهية',
      icon: 'gavel',
      color: colors.green[600],
      items: fiqhRules,
    },
    {
      id: 'sirah',
      title: 'السيرة النبوية',
      icon: 'history-edu',
      color: colors.green[700],
      items: sirahEvents,
    },
  ];

  const renderCategoryButton = (category: Category) => (
    <TouchableOpacity
      key={category.id}
      style={[
        styles.categoryButton,
        selectedCategory === category.id && {
          backgroundColor: category.color,
        },
      ]}
      onPress={() => setSelectedCategory(category.id)}>
      <Icon
        name={category.icon}
        size={24}
        color={selectedCategory === category.id ? 'white' : category.color}
      />
      <Text
        style={[
          styles.categoryButtonText,
          selectedCategory === category.id && {color: 'white'},
        ]}>
        {category.title}
      </Text>
    </TouchableOpacity>
  );

  const renderDuaItem = ({item}: {item: Dua}) => (
    <Card style={styles.itemCard}>
      <View style={styles.itemContent}>
        <View style={styles.itemHeader}>
          <Text style={styles.itemTitle}>{item.title}</Text>
          <View style={styles.itemActions}>
            <TouchableOpacity style={styles.actionButton}>
              <Icon name="share" size={20} color={colors.primary} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.actionButton}>
              <Icon name="bookmark-border" size={20} color={colors.primary} />
            </TouchableOpacity>
          </View>
        </View>
        
        <View style={styles.arabicContainer}>
          <Text style={styles.arabicText}>{item.arabic}</Text>
        </View>
        
        {item.translation && (
          <View style={styles.translationContainer}>
            <Text style={styles.translationLabel}>المعنى:</Text>
            <Text style={styles.translationText}>{item.translation}</Text>
          </View>
        )}
        
        <View style={styles.sourceContainer}>
          <Text style={styles.sourceText}>[{item.source}]</Text>
        </View>
      </View>
    </Card>
  );

  const renderFiqhItem = ({item}: {item: FiqhRule}) => (
    <Card style={styles.itemCard}>
      <View style={styles.itemContent}>
        <View style={styles.itemHeader}>
          <Text style={styles.itemTitle}>{item.title}</Text>
          <TouchableOpacity style={styles.expandButton}>
            <Icon name="expand-more" size={24} color={colors.primary} />
          </TouchableOpacity>
        </View>
        
        <Text style={styles.itemDescription}>{item.description}</Text>
        
        <View style={styles.detailsContainer}>
          <Text style={styles.detailsText}>{item.details}</Text>
        </View>
      </View>
    </Card>
  );

  const renderSirahItem = ({item}: {item: SirahEvent}) => (
    <Card style={styles.itemCard}>
      <View style={styles.itemContent}>
        <View style={styles.sirahHeader}>
          <View style={styles.sirahInfo}>
            <Text style={styles.itemTitle}>{item.title}</Text>
            {item.year && (
              <Text style={styles.yearText}>{item.year}</Text>
            )}
          </View>
          <View style={styles.timelineIndicator} />
        </View>
        
        <Text style={styles.itemDescription}>{item.description}</Text>
      </View>
    </Card>
  );

  const renderContent = () => {
    const selectedCategoryData = categories.find(cat => cat.id === selectedCategory);
    if (!selectedCategoryData) return null;

    let renderItem;
    switch (selectedCategory) {
      case 'duas':
        renderItem = renderDuaItem;
        break;
      case 'fiqh':
        renderItem = renderFiqhItem;
        break;
      case 'sirah':
        renderItem = renderSirahItem;
        break;
      default:
        renderItem = renderDuaItem;
    }

    return (
      <FlatList
        data={selectedCategoryData.items}
        renderItem={renderItem}
        keyExtractor={item => item.id.toString()}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.listContainer}
      />
    );
  };

  return (
    <View style={styles.container}>
      {/* Category Buttons */}
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.categoriesContainer}
        contentContainerStyle={styles.categoriesContent}>
        {categories.map(renderCategoryButton)}
      </ScrollView>

      {/* Content */}
      <View style={styles.contentContainer}>
        {renderContent()}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  categoriesContainer: {
    backgroundColor: colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  categoriesContent: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
  },
  categoryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
    marginRight: spacing.md,
    borderRadius: borderRadius.lg,
    backgroundColor: colors.gray[100],
    minWidth: 150,
  },
  categoryButtonText: {
    marginLeft: spacing.sm,
    fontSize: 14,
    fontWeight: '500',
    color: colors.text,
  },
  contentContainer: {
    flex: 1,
  },
  listContainer: {
    padding: spacing.lg,
  },
  itemCard: {
    backgroundColor: colors.surface,
    marginBottom: spacing.lg,
    borderRightWidth: 3,
    borderRightColor: colors.primary,
    ...shadows.sm,
  },
  itemContent: {
    padding: spacing.lg,
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.md,
  },
  itemTitle: {
    flex: 1,
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text,
  },
  itemActions: {
    flexDirection: 'row',
  },
  actionButton: {
    marginLeft: spacing.sm,
    padding: spacing.xs,
  },
  expandButton: {
    padding: spacing.xs,
  },
  arabicContainer: {
    backgroundColor: colors.green[50],
    padding: spacing.lg,
    borderRadius: borderRadius.md,
    marginBottom: spacing.md,
  },
  arabicText: {
    fontSize: 18,
    lineHeight: 32,
    color: colors.text,
    textAlign: 'center',
  },
  translationContainer: {
    marginBottom: spacing.md,
  },
  translationLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    color: colors.primary,
    marginBottom: spacing.xs,
  },
  translationText: {
    fontSize: 14,
    color: colors.textSecondary,
    lineHeight: 20,
  },
  sourceContainer: {
    alignItems: 'flex-end',
  },
  sourceText: {
    fontSize: 12,
    color: colors.gray[600],
    fontStyle: 'italic',
  },
  itemDescription: {
    fontSize: 16,
    color: colors.textSecondary,
    lineHeight: 22,
    marginBottom: spacing.md,
  },
  detailsContainer: {
    backgroundColor: colors.gray[50],
    padding: spacing.md,
    borderRadius: borderRadius.md,
    borderLeftWidth: 3,
    borderLeftColor: colors.primary,
  },
  detailsText: {
    fontSize: 14,
    color: colors.text,
    lineHeight: 20,
  },
  sirahHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  sirahInfo: {
    flex: 1,
  },
  yearText: {
    fontSize: 14,
    color: colors.primary,
    fontWeight: '500',
    marginTop: spacing.xs,
  },
  timelineIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: colors.primary,
    marginLeft: spacing.md,
  },
});
