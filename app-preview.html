<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حصن المسلم - معاينة التطبيق</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .phone-container {
            width: 375px;
            height: 812px;
            background: #000;
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            position: relative;
        }
        
        .phone-screen {
            width: 100%;
            height: 100%;
            background: #f5f5f5;
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }
        
        .status-bar {
            background: #046a38;
            color: white;
            padding: 12px 20px;
            font-size: 14px;
            font-weight: 500;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .app-content {
            background: white;
            height: calc(100% - 44px - 80px);
            overflow-y: auto;
        }
        
        .bottom-tabs {
            display: flex;
            background: white;
            border-top: 1px solid #e0e0e0;
            height: 80px;
            align-items: center;
        }
        
        .tab {
            flex: 1;
            padding: 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            border: none;
            background: none;
            color: #666;
            font-size: 12px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
        }
        
        .tab.active {
            color: #046a38;
        }
        
        .tab-icon {
            font-size: 20px;
        }
        
        .tab-content {
            display: none;
            padding: 20px;
            height: 100%;
            overflow-y: auto;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .welcome-card {
            background: linear-gradient(135deg, #046a38, #028a0f);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 25px;
            text-align: center;
        }
        
        .welcome-card h2 {
            font-size: 24px;
            margin-bottom: 8px;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 20px 0;
        }
        
        .feature-item {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            text-align: center;
            cursor: pointer;
            transition: transform 0.2s;
            border: 2px solid transparent;
        }
        
        .feature-item:hover {
            transform: translateY(-3px);
            border-color: #046a38;
        }
        
        .feature-icon {
            font-size: 32px;
            margin-bottom: 12px;
            color: #046a38;
        }
        
        .info-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            margin-top: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .info-card h3 {
            color: #046a38;
            margin-bottom: 15px;
            font-size: 18px;
        }
        
        .info-card p {
            margin-bottom: 8px;
            color: #555;
        }
        
        .surah-list {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .surah-item {
            padding: 15px 20px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .surah-item:hover {
            background: #f8f9fa;
        }
        
        .surah-item:last-child {
            border-bottom: none;
        }
        
        .surah-number {
            background: #046a38;
            color: white;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: bold;
        }
        
        .surah-info h4 {
            color: #333;
            margin-bottom: 4px;
        }
        
        .surah-info p {
            color: #666;
            font-size: 12px;
        }
        
        .prayer-card {
            background: white;
            margin: 12px 0;
            padding: 18px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .prayer-card.next {
            background: #046a38;
            color: white;
        }
        
        .prayer-name {
            font-size: 16px;
            font-weight: 600;
        }
        
        .prayer-time {
            font-size: 18px;
            font-weight: bold;
        }
        
        .dua-card {
            background: #f8f9fa;
            padding: 20px;
            margin: 15px 0;
            border-radius: 12px;
            border-right: 4px solid #046a38;
        }
        
        .arabic-text {
            font-size: 18px;
            line-height: 1.8;
            text-align: center;
            background: rgba(4, 106, 56, 0.1);
            padding: 20px;
            border-radius: 12px;
            margin: 15px 0;
            color: #333;
        }
        
        .section-title {
            color: #046a38;
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .location-info {
            background: #046a38;
            color: white;
            padding: 15px;
            border-radius: 12px;
            margin-bottom: 20px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <div class="phone-screen">
            <div class="status-bar">
                <span>9:41</span>
                <span style="font-weight: bold;">حصن المسلم</span>
                <span>🔋 100%</span>
            </div>
            
            <div class="app-content">
                <!-- Home Tab Content -->
                <div id="home-content" class="tab-content active">
                    <div class="welcome-card">
                        <h2>حصن المسلم</h2>
                        <p>تطبيق إسلامي شامل للمسلم المعاصر</p>
                    </div>
                    
                    <div class="feature-grid">
                        <div class="feature-item" onclick="showTab('quran')">
                            <div class="feature-icon">📖</div>
                            <h4>المصحف الشريف</h4>
                        </div>
                        <div class="feature-item" onclick="showTab('tafsir')">
                            <div class="feature-icon">📚</div>
                            <h4>التفسير</h4>
                        </div>
                        <div class="feature-item" onclick="showTab('muslim-kit')">
                            <div class="feature-icon">🎒</div>
                            <h4>حقيبة المسلم</h4>
                        </div>
                        <div class="feature-item" onclick="showTab('prayer-times')">
                            <div class="feature-icon">🕐</div>
                            <h4>أوقات الصلاة</h4>
                        </div>
                    </div>
                    
                    <div class="info-card">
                        <h3>معلومات التطبيق</h3>
                        <p><strong>المحرر:</strong> ياسر الجبوري</p>
                        <p><strong>البلد:</strong> العراق</p>
                        <p><strong>الإصدار:</strong> 1.0.0</p>
                    </div>
                </div>

                <!-- Quran Tab Content -->
                <div id="quran-content" class="tab-content">
                    <h2 class="section-title">المصحف الشريف</h2>
                    <div class="surah-list">
                        <div class="surah-item">
                            <div style="display: flex; align-items: center; gap: 15px;">
                                <div class="surah-number">1</div>
                                <div class="surah-info">
                                    <h4>الفاتحة</h4>
                                    <p>7 آيات • مكية</p>
                                </div>
                            </div>
                        </div>
                        <div class="surah-item">
                            <div style="display: flex; align-items: center; gap: 15px;">
                                <div class="surah-number">2</div>
                                <div class="surah-info">
                                    <h4>البقرة</h4>
                                    <p>286 آية • مدنية</p>
                                </div>
                            </div>
                        </div>
                        <div class="surah-item">
                            <div style="display: flex; align-items: center; gap: 15px;">
                                <div class="surah-number">3</div>
                                <div class="surah-info">
                                    <h4>آل عمران</h4>
                                    <p>200 آية • مدنية</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="arabic-text">
                        بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ ﴿١﴾ الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ ﴿٢﴾ الرَّحْمَٰنِ الرَّحِيمِ ﴿٣﴾ مَالِكِ يَوْمِ الدِّينِ ﴿٤﴾ إِيَّاكَ نَعْبُدُ وَإِيَّاكَ نَسْتَعِينُ ﴿٥﴾ اهْدِنَا الصِّرَاطَ الْمُسْتَقِيمَ ﴿٦﴾ صِرَاطَ الَّذِينَ أَنْعَمْتَ عَلَيْهِمْ غَيْرِ الْمَغْضُوبِ عَلَيْهِمْ وَلَا الضَّالِّينَ ﴿٧﴾
                    </div>
                </div>

                <!-- Tafsir Tab Content -->
                <div id="tafsir-content" class="tab-content">
                    <h2 class="section-title">التفسير</h2>
                    <div class="dua-card">
                        <h4 style="color: #046a38; margin-bottom: 10px;">تفسير ابن كثير - سورة الفاتحة</h4>
                        <p>الحمد لله رب العالمين: أي الثناء على الله بصفاته التي كلها أوصاف كمال، وبنعمه الظاهرة والباطنة، الدينية والدنيوية، وفي ضمنه الحمد لله...</p>
                    </div>
                    <div class="dua-card">
                        <h4 style="color: #046a38; margin-bottom: 10px;">تفسير الطبري</h4>
                        <p>قال أبو جعفر: والحمد لله الذي له الحمد في السماوات والأرض، وهو العزيز الحكيم...</p>
                    </div>
                </div>

                <!-- Muslim Kit Tab Content -->
                <div id="muslim-kit-content" class="tab-content">
                    <h2 class="section-title">حقيبة المسلم</h2>
                    <div class="feature-grid">
                        <div class="feature-item">
                            <div class="feature-icon">🤲</div>
                            <h4>الأدعية والأذكار</h4>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon">⚖️</div>
                            <h4>الأحكام الفقهية</h4>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon">👤</div>
                            <h4>السيرة النبوية</h4>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon">📿</div>
                            <h4>التسبيح</h4>
                        </div>
                    </div>

                    <div class="dua-card">
                        <h4 style="color: #046a38; margin-bottom: 10px;">دعاء الاستيقاظ</h4>
                        <div class="arabic-text">
                            الْحَمْدُ لِلَّهِ الَّذِي أَحْيَانَا بَعْدَ مَا أَمَاتَنَا وَإِلَيْهِ النُّشُورُ
                        </div>
                        <p><strong>المعنى:</strong> الحمد لله الذي أحيانا بعد أن أماتنا وإليه النشور</p>
                    </div>
                </div>

                <!-- Prayer Times Tab Content -->
                <div id="prayer-times-content" class="tab-content">
                    <h2 class="section-title">أوقات الصلاة</h2>
                    <div class="location-info">
                        <h3>📍 بغداد، العراق</h3>
                        <p>اتجاه القبلة: 225° جنوب غرب</p>
                    </div>

                    <div class="prayer-card next">
                        <div>
                            <div class="prayer-name">العصر</div>
                            <div style="font-size: 12px; opacity: 0.9;">الصلاة القادمة</div>
                        </div>
                        <div class="prayer-time">15:30</div>
                    </div>

                    <div class="prayer-card">
                        <div class="prayer-name">الفجر</div>
                        <div class="prayer-time">04:45</div>
                    </div>

                    <div class="prayer-card">
                        <div class="prayer-name">الظهر</div>
                        <div class="prayer-time">12:15</div>
                    </div>

                    <div class="prayer-card">
                        <div class="prayer-name">المغرب</div>
                        <div class="prayer-time">18:45</div>
                    </div>

                    <div class="prayer-card">
                        <div class="prayer-name">العشاء</div>
                        <div class="prayer-time">20:15</div>
                    </div>
                </div>
            </div>

            <div class="bottom-tabs">
                <button class="tab active" onclick="showTab('home')">
                    <div class="tab-icon">🏠</div>
                    <span>الرئيسية</span>
                </button>
                <button class="tab" onclick="showTab('quran')">
                    <div class="tab-icon">📖</div>
                    <span>القرآن</span>
                </button>
                <button class="tab" onclick="showTab('tafsir')">
                    <div class="tab-icon">📚</div>
                    <span>التفسير</span>
                </button>
                <button class="tab" onclick="showTab('muslim-kit')">
                    <div class="tab-icon">🎒</div>
                    <span>حقيبة المسلم</span>
                </button>
                <button class="tab" onclick="showTab('prayer-times')">
                    <div class="tab-icon">🕐</div>
                    <span>أوقات الصلاة</span>
                </button>
            </div>
        </div>
    </div>

    <script>
        function showTab(tabName) {
            // Hide all tab contents
            const contents = document.querySelectorAll('.tab-content');
            contents.forEach(content => content.classList.remove('active'));

            // Remove active class from all tabs
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => tab.classList.remove('active'));

            // Show selected tab content
            const selectedContent = document.getElementById(tabName + '-content');
            if (selectedContent) {
                selectedContent.classList.add('active');
            }

            // Add active class to selected tab
            const selectedTab = event.target.closest('.tab');
            if (selectedTab) {
                selectedTab.classList.add('active');
            }
        }
    </script>
</body>
</html>
