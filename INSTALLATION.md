# دليل تثبيت وتشغيل تطبيق حصن المسلم

## المتطلبات الأساسية

### 1. Node.js
- تثبيت Node.js الإصدار 16 أو أحدث
- تحميل من: https://nodejs.org/

### 2. Java Development Kit (JDK)
- تثبيت JDK 11 أو أحدث
- تحميل من: https://adoptium.net/

### 3. Android Studio
- تثبيت Android Studio
- تحميل من: https://developer.android.com/studio
- تأكد من تثبيت Android SDK (API Level 33)

### 4. React Native CLI
```bash
npm install -g react-native-cli
```

## خطوات التثبيت

### 1. استنساخ المشروع
```bash
git clone <repository-url>
cd hisnulmuslim
```

### 2. تثبيت التبعيات
```bash
npm install
```

### 3. تكوين متغيرات البيئة
إضافة المسارات التالية إلى متغيرات البيئة:

**Windows:**
```
ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk
JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-11.0.x.x-hotspot
```

**macOS/Linux:**
```bash
export ANDROID_HOME=$HOME/Android/Sdk
export JAVA_HOME=/usr/lib/jvm/java-11-openjdk
export PATH=$PATH:$ANDROID_HOME/emulator
export PATH=$PATH:$ANDROID_HOME/tools
export PATH=$PATH:$ANDROID_HOME/tools/bin
export PATH=$PATH:$ANDROID_HOME/platform-tools
```

### 4. تثبيت تبعيات الأندرويد
```bash
cd android
./gradlew clean
cd ..
```

### 5. ربط الأيقونات
```bash
npx react-native link react-native-vector-icons
```

## تشغيل التطبيق

### 1. تشغيل Metro Bundler
```bash
npx react-native start
```

### 2. تشغيل التطبيق على الأندرويد

**باستخدام جهاز حقيقي:**
- تفعيل وضع المطور على الجهاز
- تفعيل USB Debugging
- توصيل الجهاز بالكمبيوتر
```bash
npx react-native run-android
```

**باستخدام المحاكي:**
- فتح Android Studio
- تشغيل AVD Manager
- إنشاء وتشغيل محاكي أندرويد
```bash
npx react-native run-android
```

## حل المشاكل الشائعة

### 1. خطأ في ANDROID_HOME
```bash
echo $ANDROID_HOME  # للتأكد من المسار
```

### 2. خطأ في Gradle
```bash
cd android
./gradlew clean
./gradlew build
```

### 3. خطأ في Metro
```bash
npx react-native start --reset-cache
```

### 4. خطأ في الأيقونات
```bash
npx react-native unlink react-native-vector-icons
npx react-native link react-native-vector-icons
```

### 5. مشكلة في الأذونات (Linux/macOS)
```bash
chmod +x android/gradlew
```

## بناء APK للإنتاج

### 1. إنشاء مفتاح التوقيع
```bash
keytool -genkeypair -v -keystore hisnulmuslim-release-key.keystore -alias hisnulmuslim-key-alias -keyalg RSA -keysize 2048 -validity 10000
```

### 2. تكوين ملف gradle.properties
إضافة المعلومات التالية:
```
MYAPP_RELEASE_STORE_FILE=hisnulmuslim-release-key.keystore
MYAPP_RELEASE_KEY_ALIAS=hisnulmuslim-key-alias
MYAPP_RELEASE_STORE_PASSWORD=*****
MYAPP_RELEASE_KEY_PASSWORD=*****
```

### 3. بناء APK
```bash
cd android
./gradlew assembleRelease
```

سيتم إنشاء APK في:
`android/app/build/outputs/apk/release/app-release.apk`

## اختبار التطبيق

### 1. تشغيل الاختبارات
```bash
npm test
```

### 2. فحص الكود
```bash
npm run lint
```

## نصائح مهمة

1. **تأكد من تشغيل Metro Bundler** قبل تشغيل التطبيق
2. **استخدم جهاز حقيقي** للحصول على أفضل أداء
3. **نظف الكاش** عند مواجهة مشاكل غريبة
4. **تحديث التبعيات** بانتظام للحصول على أحدث الميزات

## الدعم الفني

في حالة مواجهة مشاكل:
1. تحقق من سجل الأخطاء في Metro Bundler
2. تحقق من سجل الأخطاء في Android Studio
3. ابحث عن الحلول في مجتمع React Native
4. تواصل مع المطور ياسر الجبوري

---

**بالتوفيق في تشغيل التطبيق! 🚀**
