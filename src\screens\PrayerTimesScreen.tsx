import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
} from 'react-native';
import {Card} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {colors, spacing, borderRadius, shadows} from '../theme/theme';

interface PrayerTime {
  name: string;
  time: string;
  arabicName: string;
  isNext?: boolean;
  isCurrent?: boolean;
}

interface DayPrayerTimes {
  date: string;
  hijriDate: string;
  fajr: string;
  sunrise: string;
  dhuhr: string;
  asr: string;
  maghrib: string;
  isha: string;
}

const PrayerTimesScreen = () => {
  const [currentLocation, setCurrentLocation] = useState('بغداد، العراق');
  const [currentDate, setCurrentDate] = useState(new Date());
  const [nextPrayer, setNextPrayer] = useState('الظهر');

  // Sample prayer times data
  const todayPrayerTimes: PrayerTime[] = [
    {name: 'الفجر', arabicName: 'الفجر', time: '4:30 ص'},
    {name: 'الشروق', arabicName: 'الشروق', time: '5:45 ص'},
    {name: 'الظهر', arabicName: 'الظهر', time: '12:15 م', isNext: true},
    {name: 'العصر', arabicName: 'العصر', time: '3:30 م'},
    {name: 'المغرب', arabicName: 'المغرب', time: '6:45 م'},
    {name: 'العشاء', arabicName: 'العشاء', time: '8:15 م'},
  ];

  const weekPrayerTimes: DayPrayerTimes[] = [
    {
      date: 'الأحد 15 يناير',
      hijriDate: '23 جمادى الآخرة 1445',
      fajr: '4:30 ص',
      sunrise: '5:45 ص',
      dhuhr: '12:15 م',
      asr: '3:30 م',
      maghrib: '6:45 م',
      isha: '8:15 م',
    },
    {
      date: 'الإثنين 16 يناير',
      hijriDate: '24 جمادى الآخرة 1445',
      fajr: '4:29 ص',
      sunrise: '5:44 ص',
      dhuhr: '12:15 م',
      asr: '3:31 م',
      maghrib: '6:46 م',
      isha: '8:16 م',
    },
    {
      date: 'الثلاثاء 17 يناير',
      hijriDate: '25 جمادى الآخرة 1445',
      fajr: '4:28 ص',
      sunrise: '5:43 ص',
      dhuhr: '12:15 م',
      asr: '3:32 م',
      maghrib: '6:47 م',
      isha: '8:17 م',
    },
  ];

  const iraqiCities = [
    'بغداد',
    'البصرة',
    'الموصل',
    'أربيل',
    'النجف',
    'كربلاء',
    'السليمانية',
    'الأنبار',
    'ديالى',
    'كركوك',
  ];

  const renderPrayerTimeCard = (prayer: PrayerTime, index: number) => (
    <Card
      key={index}
      style={[
        styles.prayerCard,
        prayer.isNext && styles.nextPrayerCard,
        prayer.isCurrent && styles.currentPrayerCard,
      ]}>
      <View style={styles.prayerContent}>
        <View style={styles.prayerInfo}>
          <Text
            style={[
              styles.prayerName,
              (prayer.isNext || prayer.isCurrent) && styles.highlightedText,
            ]}>
            {prayer.name}
          </Text>
          {prayer.name === 'الفجر' && (
            <Text style={styles.additionalInfo}>الإمساك: 4:20 ص</Text>
          )}
          {prayer.name === 'المغرب' && (
            <Text style={styles.additionalInfo}>الإفطار: 6:45 م</Text>
          )}
        </View>
        <View style={styles.prayerTimeContainer}>
          <Text
            style={[
              styles.prayerTime,
              (prayer.isNext || prayer.isCurrent) && styles.highlightedTime,
            ]}>
            {prayer.time}
          </Text>
          {prayer.isNext && (
            <View style={styles.nextIndicator}>
              <Text style={styles.nextText}>التالية</Text>
            </View>
          )}
        </View>
      </View>
    </Card>
  );

  const renderWeekRow = (day: DayPrayerTimes, index: number) => (
    <View key={index} style={styles.weekRow}>
      <View style={styles.dateColumn}>
        <Text style={styles.dateText}>{day.date}</Text>
        <Text style={styles.hijriText}>{day.hijriDate}</Text>
      </View>
      <Text style={styles.timeCell}>{day.fajr}</Text>
      <Text style={styles.timeCell}>{day.dhuhr}</Text>
      <Text style={styles.timeCell}>{day.asr}</Text>
      <Text style={styles.timeCell}>{day.maghrib}</Text>
      <Text style={styles.timeCell}>{day.isha}</Text>
    </View>
  );

  const showLocationPicker = () => {
    Alert.alert(
      'اختيار الموقع',
      'اختر مدينتك:',
      iraqiCities.map(city => ({
        text: city,
        onPress: () => setCurrentLocation(`${city}، العراق`),
      })).concat([{text: 'إلغاء', style: 'cancel'}])
    );
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Header */}
      <Card style={styles.headerCard}>
        <View style={styles.headerContent}>
          <View style={styles.locationInfo}>
            <View style={styles.locationRow}>
              <Icon name="location-on" size={20} color={colors.primary} />
              <Text style={styles.locationText}>{currentLocation}</Text>
              <TouchableOpacity onPress={showLocationPicker}>
                <Icon name="edit" size={20} color={colors.primary} />
              </TouchableOpacity>
            </View>
            <Text style={styles.dateText}>
              اليوم: الأحد 15 يناير 2024
            </Text>
            <Text style={styles.hijriDateText}>
              23 جمادى الآخرة 1445هـ
            </Text>
          </View>
          
          <View style={styles.nextPrayerInfo}>
            <Text style={styles.nextPrayerLabel}>الصلاة التالية:</Text>
            <Text style={styles.nextPrayerName}>{nextPrayer}</Text>
            <Text style={styles.remainingTime}>بعد ساعتين و 30 دقيقة</Text>
          </View>
        </View>
      </Card>

      {/* Today's Prayer Times */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>أوقات الصلاة اليوم</Text>
        <View style={styles.prayerTimesGrid}>
          {todayPrayerTimes.map(renderPrayerTimeCard)}
        </View>
      </View>

      {/* Qibla Direction */}
      <Card style={styles.qiblaCard}>
        <View style={styles.qiblaContent}>
          <View style={styles.qiblaInfo}>
            <Icon name="explore" size={32} color={colors.primary} />
            <View style={styles.qiblaText}>
              <Text style={styles.qiblaTitle}>اتجاه القبلة</Text>
              <Text style={styles.qiblaDirection}>225° جنوب غرب</Text>
            </View>
          </View>
          <TouchableOpacity style={styles.compassButton}>
            <Text style={styles.compassButtonText}>فتح البوصلة</Text>
          </TouchableOpacity>
        </View>
      </Card>

      {/* Weekly Prayer Times */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>أوقات الصلاة للأسبوع</Text>
        <Card style={styles.weeklyCard}>
          <View style={styles.weeklyHeader}>
            <Text style={styles.weeklyHeaderCell}>التاريخ</Text>
            <Text style={styles.weeklyHeaderCell}>الفجر</Text>
            <Text style={styles.weeklyHeaderCell}>الظهر</Text>
            <Text style={styles.weeklyHeaderCell}>العصر</Text>
            <Text style={styles.weeklyHeaderCell}>المغرب</Text>
            <Text style={styles.weeklyHeaderCell}>العشاء</Text>
          </View>
          {weekPrayerTimes.map(renderWeekRow)}
        </Card>
      </View>

      {/* Settings */}
      <Card style={styles.settingsCard}>
        <View style={styles.settingsContent}>
          <Text style={styles.settingsTitle}>الإعدادات</Text>
          
          <TouchableOpacity style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Icon name="notifications" size={24} color={colors.primary} />
              <Text style={styles.settingText}>تنبيهات الصلاة</Text>
            </View>
            <Icon name="chevron-right" size={24} color={colors.gray[400]} />
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Icon name="volume-up" size={24} color={colors.primary} />
              <Text style={styles.settingText}>الأذان</Text>
            </View>
            <Icon name="chevron-right" size={24} color={colors.gray[400]} />
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Icon name="calculate" size={24} color={colors.primary} />
              <Text style={styles.settingText}>طريقة الحساب</Text>
            </View>
            <Icon name="chevron-right" size={24} color={colors.gray[400]} />
          </TouchableOpacity>
        </View>
      </Card>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  headerCard: {
    margin: spacing.lg,
    backgroundColor: colors.surface,
    ...shadows.md,
  },
  headerContent: {
    padding: spacing.lg,
  },
  locationInfo: {
    marginBottom: spacing.lg,
  },
  locationRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  locationText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text,
    marginLeft: spacing.sm,
    flex: 1,
  },
  dateText: {
    fontSize: 16,
    color: colors.textSecondary,
    marginBottom: spacing.xs,
  },
  hijriDateText: {
    fontSize: 14,
    color: colors.primary,
  },
  nextPrayerInfo: {
    backgroundColor: colors.green[50],
    padding: spacing.lg,
    borderRadius: borderRadius.md,
    alignItems: 'center',
  },
  nextPrayerLabel: {
    fontSize: 14,
    color: colors.textSecondary,
    marginBottom: spacing.xs,
  },
  nextPrayerName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.primary,
    marginBottom: spacing.xs,
  },
  remainingTime: {
    fontSize: 16,
    color: colors.green[700],
  },
  section: {
    marginHorizontal: spacing.lg,
    marginBottom: spacing.lg,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: spacing.lg,
    textAlign: 'center',
  },
  prayerTimesGrid: {
    gap: spacing.md,
  },
  prayerCard: {
    backgroundColor: colors.surface,
    ...shadows.sm,
  },
  nextPrayerCard: {
    backgroundColor: colors.primary,
  },
  currentPrayerCard: {
    backgroundColor: colors.green[600],
  },
  prayerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: spacing.lg,
  },
  prayerInfo: {
    flex: 1,
  },
  prayerName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: spacing.xs,
  },
  highlightedText: {
    color: 'white',
  },
  additionalInfo: {
    fontSize: 12,
    color: colors.textSecondary,
  },
  prayerTimeContainer: {
    alignItems: 'flex-end',
  },
  prayerTime: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.primary,
  },
  highlightedTime: {
    color: 'white',
  },
  nextIndicator: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.sm,
    marginTop: spacing.xs,
  },
  nextText: {
    fontSize: 12,
    color: 'white',
    fontWeight: 'bold',
  },
  qiblaCard: {
    margin: spacing.lg,
    backgroundColor: colors.surface,
    ...shadows.sm,
  },
  qiblaContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: spacing.lg,
  },
  qiblaInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  qiblaText: {
    marginLeft: spacing.md,
  },
  qiblaTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: spacing.xs,
  },
  qiblaDirection: {
    fontSize: 16,
    color: colors.primary,
    fontWeight: '500',
  },
  compassButton: {
    backgroundColor: colors.primary,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
    borderRadius: borderRadius.md,
  },
  compassButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
  weeklyCard: {
    backgroundColor: colors.surface,
    ...shadows.sm,
  },
  weeklyHeader: {
    flexDirection: 'row',
    backgroundColor: colors.primary,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.sm,
  },
  weeklyHeaderCell: {
    flex: 1,
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  weekRow: {
    flexDirection: 'row',
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  dateColumn: {
    flex: 1,
  },
  timeCell: {
    flex: 1,
    fontSize: 12,
    color: colors.text,
    textAlign: 'center',
  },
  settingsCard: {
    margin: spacing.lg,
    backgroundColor: colors.surface,
    ...shadows.sm,
  },
  settingsContent: {
    padding: spacing.lg,
  },
  settingsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: spacing.lg,
    textAlign: 'center',
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  settingInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingText: {
    fontSize: 16,
    color: colors.text,
    marginLeft: spacing.md,
  },
});
