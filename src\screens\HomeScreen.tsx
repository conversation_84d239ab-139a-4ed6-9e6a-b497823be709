import React from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import {Card, Button} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {colors, spacing, borderRadius, shadows} from '../theme/theme';

const {width} = Dimensions.get('window');

const HomeScreen = ({navigation}: any) => {
  const features = [
    {
      id: 1,
      title: 'المصحف الشريف',
      description: 'اقرأ القرآن الكريم بخط واضح',
      icon: 'book',
      screen: 'Quran',
      color: colors.primary,
    },
    {
      id: 2,
      title: 'التفسير',
      description: 'تفسير القرآن الكريم',
      icon: 'library-books',
      screen: 'Tafsir',
      color: colors.green[600],
    },
    {
      id: 3,
      title: 'حقيبة المسلم',
      description: 'الأدعية والأذكار والأحكام',
      icon: 'favorite',
      screen: 'MuslimKit',
      color: colors.green[700],
    },
    {
      id: 4,
      title: 'أوقات الصلاة',
      description: 'مواقيت الصلاة في العراق',
      icon: 'access-time',
      screen: 'PrayerTimes',
      color: colors.green[800],
    },
  ];

  const renderFeatureCard = (feature: any) => (
    <TouchableOpacity
      key={feature.id}
      style={styles.featureCard}
      onPress={() => navigation.navigate(feature.screen)}>
      <Card style={[styles.card, {borderLeftColor: feature.color}]}>
        <View style={styles.cardContent}>
          <View style={[styles.iconContainer, {backgroundColor: feature.color}]}>
            <Icon name={feature.icon} size={30} color="white" />
          </View>
          <View style={styles.textContainer}>
            <Text style={styles.featureTitle}>{feature.title}</Text>
            <Text style={styles.featureDescription}>{feature.description}</Text>
          </View>
          <Icon name="chevron-right" size={24} color={colors.gray[400]} />
        </View>
      </Card>
    </TouchableOpacity>
  );

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Header Section */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <Text style={styles.welcomeText}>أهلاً وسهلاً</Text>
          <Text style={styles.appTitle}>حصن المسلم</Text>
          <Text style={styles.subtitle}>
            تطبيق إسلامي شامل للمصحف الشريف والتفسير وحقيبة المسلم
          </Text>
        </View>
      </View>

      {/* Quick Actions */}
      <View style={styles.quickActions}>
        <Text style={styles.sectionTitle}>الميزات الرئيسية</Text>
        {features.map(renderFeatureCard)}
      </View>

      {/* About Section */}
      <Card style={styles.aboutCard}>
        <View style={styles.aboutContent}>
          <Text style={styles.aboutTitle}>عن التطبيق</Text>
          <Text style={styles.aboutText}>
            تطبيق حصن المسلم هو تطبيق إسلامي شامل يحتوي على المصحف الشريف مع
            التفسير، وحقيبة المسلم التي تشمل الأدعية والأذكار والأحكام الفقهية
            والسيرة النبوية، بالإضافة إلى أوقات الصلاة.
          </Text>
          <View style={styles.authorInfo}>
            <Text style={styles.authorLabel}>المحرر:</Text>
            <Text style={styles.authorName}>ياسر الجبوري</Text>
          </View>
          <View style={styles.countryInfo}>
            <Text style={styles.countryLabel}>البلد:</Text>
            <Text style={styles.countryName}>العراق</Text>
          </View>
        </View>
      </Card>

      {/* Footer */}
      <View style={styles.footer}>
        <Text style={styles.footerText}>
          © 2024 حصن المسلم - جميع الحقوق محفوظة
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    backgroundColor: colors.primary,
    paddingVertical: spacing.xl,
    paddingHorizontal: spacing.lg,
    borderBottomLeftRadius: borderRadius.xl,
    borderBottomRightRadius: borderRadius.xl,
  },
  headerContent: {
    alignItems: 'center',
  },
  welcomeText: {
    fontSize: 16,
    color: 'white',
    marginBottom: spacing.xs,
  },
  appTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: spacing.sm,
  },
  subtitle: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    lineHeight: 20,
  },
  quickActions: {
    padding: spacing.lg,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: spacing.lg,
    textAlign: 'center',
  },
  featureCard: {
    marginBottom: spacing.md,
  },
  card: {
    backgroundColor: colors.surface,
    borderLeftWidth: 4,
    ...shadows.sm,
  },
  cardContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.lg,
  },
  iconContainer: {
    width: 60,
    height: 60,
    borderRadius: borderRadius.lg,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  textContainer: {
    flex: 1,
  },
  featureTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: spacing.xs,
  },
  featureDescription: {
    fontSize: 14,
    color: colors.textSecondary,
    lineHeight: 18,
  },
  aboutCard: {
    margin: spacing.lg,
    backgroundColor: colors.surface,
    ...shadows.md,
  },
  aboutContent: {
    padding: spacing.lg,
  },
  aboutTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.primary,
    marginBottom: spacing.md,
    textAlign: 'center',
  },
  aboutText: {
    fontSize: 14,
    color: colors.textSecondary,
    lineHeight: 22,
    textAlign: 'justify',
    marginBottom: spacing.lg,
  },
  authorInfo: {
    flexDirection: 'row',
    marginBottom: spacing.sm,
  },
  authorLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.text,
    marginRight: spacing.sm,
  },
  authorName: {
    fontSize: 16,
    color: colors.primary,
    fontWeight: '500',
  },
  countryInfo: {
    flexDirection: 'row',
  },
  countryLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.text,
    marginRight: spacing.sm,
  },
  countryName: {
    fontSize: 16,
    color: colors.primary,
    fontWeight: '500',
  },
  footer: {
    padding: spacing.lg,
    alignItems: 'center',
  },
  footerText: {
    fontSize: 12,
    color: colors.textSecondary,
    textAlign: 'center',
  },
});
